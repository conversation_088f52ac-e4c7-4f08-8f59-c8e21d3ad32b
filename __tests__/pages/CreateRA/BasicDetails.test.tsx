// import React from 'react';
// import {render, screen, fireEvent, act, waitFor} from '@testing-library/react';
// import userEvent from '@testing-library/user-event';
// import '@testing-library/jest-dom';
// import {BasicDetails} from '../../../src/pages/CreateRA/BasicDetails';
// import {TemplateForm} from '../../../src/types/template';
// import {RiskForm} from '../../../src/types/risk';
// import {
//   getApprovalsRequiredList,
//   getOfficesList,
//   getVesselsList,
// } from '../../../src/services/services';
// import {formatDateToYYYYMMDD} from '../../../src/utils/helper';

// // Mock services
// jest.mock('../../../src/services/services', () => ({
//   getApprovalsRequiredList: jest.fn(),
//   getOfficesList: jest.fn(),
//   getVesselsList: jest.fn(),
// }));

// // Mock helper functions
// jest.mock('../../../src/utils/helper', () => ({
//   formatDateToYYYYMMDD: jest.fn(),
// }));

// // Mock DropdownTypeahead
// jest.mock('../../../src/components/DropdownTypeahead', () => ({
//   __esModule: true,
//   default: ({
//     label,
//     options,
//     selected,
//     onChange,
//     onInputChange,
//     onBlur,
//     isInvalid,
//     errorMessage,
//     multiple,
//   }: any) => (
//     <div data-testid={`dropdown-${label.toLowerCase().replace(/[^a-z0-9]/g, '-')}`}>
//       <label>{label}</label>
//       <select
//         data-testid={`${label.toLowerCase().replace(/[^a-z0-9]/g, '-')}-select`}
//         multiple={multiple}
//         onChange={(e) => {
//           const value = multiple
//             ? Array.from(e.target.selectedOptions, option => ({value: parseInt(option.value), label: option.text}))
//             : {value: parseInt(e.target.value), label: e.target.options[e.target.selectedIndex]?.text};
//           onChange(value);
//         }}
//         onFocus={onInputChange}
//         onBlur={onBlur}
//         className={isInvalid ? 'is-invalid' : ''}
//       >
//         <option value="">Select...</option>
//         {options?.map((option: any) => (
//           <option key={option.value} value={option.value}>
//             {option.label}
//           </option>
//         ))}
//       </select>
//       {isInvalid && <div data-testid={`error-${label.toLowerCase().replace(/[^a-z0-9]/g, '-')}`}>{errorMessage}</div>}
//     </div>
//   ),
// }));

// // Mock CustomDatePicker
// jest.mock('../../../src/components/CustomDatePicker', () => ({
//   __esModule: true,
//   default: ({
//     label,
//     value,
//     onChange,
//     placeholder,
//     controlId,
//     isRequired,
//     errorMsg,
//   }: any) => (
//     <div data-testid={`datepicker-${controlId}`}>
//       <label>{label}</label>
//       <input
//         data-testid={`${controlId}-input`}
//         type="date"
//         value={value ? value.toISOString().split('T')[0] : ''}
//         onChange={(e) => onChange(e.target.value ? new Date(e.target.value) : undefined)}
//         placeholder={placeholder}
//         className={errorMsg ? 'is-invalid' : ''}
//       />
//       {errorMsg && <div data-testid={`error-${controlId}`}>{errorMsg}</div>}
//     </div>
//   ),
// }));

// // Mock InputComponent
// jest.mock('../../../src/components/InputComponent', () => ({
//   InputComponent: ({
//     label,
//     name,
//     value,
//     onChange,
//     onBlur,
//     error,
//     placeholder,
//     type,
//     maxLength,
//     rows,
//     form,
//   }: any) => (
//     <div data-testid={`input-${name}`}>
//       <label htmlFor={name}>{label}</label>
//       {type === 'textarea' ? (
//         <textarea
//           id={name}
//           name={name}
//           value={form?.[name] || value || ''}
//           onChange={onChange}
//           onBlur={onBlur}
//           placeholder={placeholder}
//           maxLength={maxLength}
//           rows={rows}
//         />
//       ) : (
//         <input
//           id={name}
//           name={name}
//           value={form?.[name] || value || ''}
//           onChange={onChange}
//           onBlur={onBlur}
//           placeholder={placeholder}
//           maxLength={maxLength}
//           type={type || 'text'}
//         />
//       )}
//       <div data-testid={`error-${name}`}>{error || ''}</div>
//     </div>
//   ),
// }));

// describe('BasicDetails Component', () => {
//   const mockSetForm = jest.fn();
//   const mockOnValidate = jest.fn();
//   const mockGetVesselsList = getVesselsList as jest.MockedFunction<typeof getVesselsList>;
//   const mockGetOfficesList = getOfficesList as jest.MockedFunction<typeof getOfficesList>;
//   const mockGetApprovalsRequiredList = getApprovalsRequiredList as jest.MockedFunction<typeof getApprovalsRequiredList>;
//   const mockFormatDateToYYYYMMDD = formatDateToYYYYMMDD as jest.MockedFunction<typeof formatDateToYYYYMMDD>;

//   const defaultForm: TemplateForm = {
//     task_requiring_ra: '',
//     task_duration: '',
//     task_duration_unit: null,
//     task_alternative_consideration: '',
//     task_rejection_reason: '',
//     worst_case_scenario: '',
//     recovery_measures: '',
//     status: '',
//     parameters: [],
//     risk_template_category: {
//       risk_template_category_id: [],
//       is_other: false,
//       value: '',
//     },
//     risk_template_hazard: {
//       risk_template_hazard_id: [],
//       is_other: false,
//       value: '',
//     },
//     risk_template_job: [],
//     risk_template_task_reliability_assessment: [],
//   };

//   const defaultRiskForm: RiskForm = {
//     task_requiring_ra: '',
//     assessor: 0,
//     vessel_ownership_id: 0,
//     date_risk_assessment: '',
//     task_duration: '',
//     task_alternative_consideration: '',
//     task_rejection_reason: '',
//     worst_case_scenario: '',
//     recovery_measures: '',
//     status: '',
//     approval_required: [],
//     risk_team_member: [],
//     risk_category: {
//       is_other: false,
//       category_id: [],
//       value: '',
//     },
//     risk_hazard: {
//       is_other: false,
//       hazard_id: [],
//       value: '',
//     },
//     parameters: [],
//     risk_job: [],
//     risk_task_reliability_assessment: [],
//   };

//   const mockVesselOptions = [
//     {id: 1, name: 'Vessel 1', vessel: {id: 101}},
//     {id: 2, name: 'Vessel 2', vessel: {id: 102}},
//   ];

//   const mockOfficeOptions = [
//     {id: 1, value: 'Office 1'},
//     {id: 2, value: 'Office 2'},
//   ];

//   const mockApprovalOptions = [
//     {id: 1, name: 'Approval 1'},
//     {id: 2, name: 'Approval 2'},
//     {id: 3, name: 'Approval 3'},
//   ];

//   beforeEach(() => {
//     jest.clearAllMocks();
//     mockGetVesselsList.mockResolvedValue(mockVesselOptions);
//     mockGetOfficesList.mockResolvedValue(mockOfficeOptions);
//     mockGetApprovalsRequiredList.mockResolvedValue(mockApprovalOptions);
//     mockFormatDateToYYYYMMDD.mockImplementation((date) => date.toISOString().split('T')[0]);
//   });

//   const renderBasicDetails = (
//     form = defaultForm,
//     onValidate = mockOnValidate,
//     type = 'template',
//   ) => {
//     const ref = React.createRef<{validate: () => boolean}>();
//     const result = render(
//       <BasicDetails
//         ref={ref}
//         form={form}
//         setForm={mockSetForm}
//         onValidate={onValidate}
//         type={type}
//       />,
//     );
//     return {...result, ref};
//   };

//   describe('Rendering', () => {
//     it('renders the component with all required fields', () => {
//       renderBasicDetails();

//       expect(screen.getByText('Enter Basic RA Details')).toBeInTheDocument();
//       expect(screen.getByLabelText('Task Requiring R.A.')).toBeInTheDocument();
//       expect(screen.getByLabelText('Duration of Task')).toBeInTheDocument();
//       expect(
//         screen.getByLabelText('Alternative Considered to carry out above task'),
//       ).toBeInTheDocument();
//       expect(screen.getByLabelText('Reason for Rejecting Alternatives')).toBeInTheDocument();
//     });

//     it('renders input fields with correct attributes', () => {
//       renderBasicDetails();

//       const taskRequiringInput = screen.getByLabelText('Task Requiring R.A.');
//       expect(taskRequiringInput).toHaveAttribute('name', 'task_requiring_ra');
//       expect(taskRequiringInput).toHaveAttribute(
//         'placeholder',
//         'Enter the task requiring risk assessment',
//       );
//       expect(taskRequiringInput).toHaveAttribute('maxLength', '255');

//       const durationInput = screen.getByLabelText('Duration of Task');
//       expect(durationInput).toHaveAttribute('name', 'task_duration');
//       expect(durationInput).toHaveAttribute(
//         'placeholder',
//         'Enter No. of Days/Hours Required',
//       );

//       const alternativeInput = screen.getByLabelText(
//         'Alternative Considered to carry out above task',
//       );
//       expect(alternativeInput).toHaveAttribute(
//         'name',
//         'task_alternative_consideration',
//       );

//       const rejectionInput = screen.getByLabelText('Reason for Rejecting Alternatives');
//       expect(rejectionInput).toHaveAttribute('name', 'task_rejection_reason');
//       expect(rejectionInput).toHaveAttribute('maxLength', '4000');
//     });

//     it('renders textarea for reason for rejection field', () => {
//       renderBasicDetails();

//       const rejectionInput = screen.getByLabelText('Reason for Rejecting Alternatives');
//       expect(rejectionInput.tagName).toBe('TEXTAREA');
//       expect(rejectionInput).toHaveAttribute('rows', '3');
//     });

//     it('displays form values correctly', () => {
//       const formWithValues = {
//         ...defaultForm,
//         task_requiring_ra: 'Test task',
//         task_duration: '5',
//         task_alternative_consideration: 'Test alternative',
//         task_rejection_reason: 'Test rejection reason',
//       };

//       renderBasicDetails(formWithValues);

//       expect(screen.getByDisplayValue('Test task')).toBeInTheDocument();
//       expect(screen.getByDisplayValue('5')).toBeInTheDocument();
//       expect(screen.getByDisplayValue('Test alternative')).toBeInTheDocument();
//       expect(
//         screen.getByDisplayValue('Test rejection reason'),
//       ).toBeInTheDocument();
//     });
//   });

//   describe('Form Interactions', () => {
//     it('calls setForm when input values change', async () => {
//       const user = userEvent.setup();
//       renderBasicDetails();

//       const taskInput = screen.getByLabelText('Task Requiring R.A.');
//       await user.type(taskInput, 'New task');

//       expect(mockSetForm).toHaveBeenCalled();
//     });

//     it('handles onChange events correctly', () => {
//       renderBasicDetails();

//       const taskInput = screen.getByLabelText('Task Requiring R.A.');
//       fireEvent.change(taskInput, {
//         target: {name: 'task_requiring_ra', value: 'Test task'},
//       });

//       expect(mockSetForm).toHaveBeenCalledWith({
//         ...defaultForm,
//         task_requiring_ra: 'Test task',
//       });
//     });

//     it('handles onBlur events correctly', () => {
//       renderBasicDetails();

//       const taskInput = screen.getByLabelText('Task Requiring R.A.');
//       fireEvent.blur(taskInput, {target: {name: 'task_requiring_ra'}});

//       // Should trigger validation
//       expect(mockOnValidate).toHaveBeenCalled();
//     });

//     it('updates touched state on blur', () => {
//       renderBasicDetails();

//       const taskInput = screen.getByLabelText('Task Requiring R.A.');
//       fireEvent.blur(taskInput, {target: {name: 'task_requiring_ra'}});

//       // After blur, error should be visible for empty field
//       expect(screen.getByTestId('error-task_requiring_ra')).toHaveTextContent(
//         'This is a mandatory field. Please fill to process.',
//       );
//     });

//     it('clears validation error when field is filled', () => {
//       renderBasicDetails();

//       const taskInput = screen.getByLabelText('Task Requiring R.A.');

//       // First trigger error by blurring empty field
//       fireEvent.blur(taskInput, {target: {name: 'task_requiring_ra'}});
//       expect(screen.getByTestId('error-task_requiring_ra')).toHaveTextContent(
//         'This is a mandatory field. Please fill to process.',
//       );

//       // Then fill the field
//       fireEvent.change(taskInput, {
//         target: {name: 'task_requiring_ra', value: 'Test task'},
//       });

//       // Error should be cleared
//       expect(screen.getByTestId('error-task_requiring_ra')).toHaveTextContent(
//         '',
//       );
//     });

//     it('handles duration field as string conversion', () => {
//       renderBasicDetails();

//       const durationInput = screen.getByLabelText('Duration of Task');
//       fireEvent.change(durationInput, {
//         target: {name: 'task_duration', value: '5'},
//       });

//       expect(mockSetForm).toHaveBeenCalledWith({
//         ...defaultForm,
//         task_duration: '5',
//       });
//     });
//   });

//   describe('Validation', () => {
//     it('validates all required fields are empty initially', () => {
//       const {ref} = renderBasicDetails();

//       let isValid;
//       act(() => {
//         isValid = ref.current?.validate();
//       });
//       expect(isValid).toBe(false);
//       expect(mockOnValidate).toHaveBeenCalledWith(false);
//     });

//     it('validates successfully when all fields are filled', () => {
//       const formWithAllFields = {
//         ...defaultForm,
//         task_requiring_ra: 'Test task',
//         task_duration: '5',
//         task_alternative_consideration: 'Test alternative',
//         task_rejection_reason: 'Test rejection',
//       };

//       const {ref} = renderBasicDetails(formWithAllFields);

//       let isValid;
//       act(() => {
//         isValid = ref.current?.validate();
//       });
//       expect(isValid).toBe(true);
//       expect(mockOnValidate).toHaveBeenCalledWith(true);
//     });

//     it('fails validation when task_requiring_ra is empty', () => {
//       const formWithMissingTask = {
//         ...defaultForm,
//         task_requiring_ra: '',
//         task_duration: '5',
//         task_alternative_consideration: 'Test alternative',
//         task_rejection_reason: 'Test rejection',
//       };

//       const {ref} = renderBasicDetails(formWithMissingTask);

//       let isValid;
//       act(() => {
//         isValid = ref.current?.validate();
//       });
//       expect(isValid).toBe(false);
//       expect(mockOnValidate).toHaveBeenCalledWith(false);
//     });

//     it('fails validation when task_duration is empty', () => {
//       const formWithMissingDuration = {
//         ...defaultForm,
//         task_requiring_ra: 'Test task',
//         task_duration: '',
//         task_alternative_consideration: 'Test alternative',
//         task_rejection_reason: 'Test rejection',
//       };

//       const {ref} = renderBasicDetails(formWithMissingDuration);

//       let isValid;
//       act(() => {
//         isValid = ref.current?.validate();
//       });
//       expect(isValid).toBe(false);
//       expect(mockOnValidate).toHaveBeenCalledWith(false);
//     });

//     it('fails validation when task_alternative_consideration is empty', () => {
//       const formWithMissingAlternative = {
//         ...defaultForm,
//         task_requiring_ra: 'Test task',
//         task_duration: '5',
//         task_alternative_consideration: '',
//         task_rejection_reason: 'Test rejection',
//       };

//       const {ref} = renderBasicDetails(formWithMissingAlternative);

//       let isValid;
//       act(() => {
//         isValid = ref.current?.validate();
//       });
//       expect(isValid).toBe(false);
//       expect(mockOnValidate).toHaveBeenCalledWith(false);
//     });

//     it('fails validation when task_rejection_reason is empty', () => {
//       const formWithMissingRejection = {
//         ...defaultForm,
//         task_requiring_ra: 'Test task',
//         task_duration: '5',
//         task_alternative_consideration: 'Test alternative',
//         task_rejection_reason: '',
//       };

//       const {ref} = renderBasicDetails(formWithMissingRejection);

//       let isValid;
//       act(() => {
//         isValid = ref.current?.validate();
//       });
//       expect(isValid).toBe(false);
//       expect(mockOnValidate).toHaveBeenCalledWith(false);
//     });

//     it('handles whitespace-only values as invalid', () => {
//       const formWithWhitespace = {
//         ...defaultForm,
//         task_requiring_ra: '   ',
//         task_duration: '5',
//         task_alternative_consideration: 'Test alternative',
//         task_rejection_reason: 'Test rejection',
//       };

//       const {ref} = renderBasicDetails(formWithWhitespace);

//       let isValid;
//       act(() => {
//         isValid = ref.current?.validate();
//       });
//       expect(isValid).toBe(false);
//       expect(mockOnValidate).toHaveBeenCalledWith(false);
//     });

//     it('validates with form containing all required fields', () => {
//       const customForm = {
//         ...defaultForm,
//         task_requiring_ra: 'Custom task',
//         task_duration: '3',
//         task_alternative_consideration: 'Custom alternative',
//         task_rejection_reason: 'Custom rejection',
//       };

//       const {ref} = renderBasicDetails(customForm);

//       let isValid;
//       act(() => {
//         isValid = ref.current?.validate();
//       });
//       expect(isValid).toBe(true);
//     });

//     it('handles null task_duration correctly', () => {
//       const formWithNullDuration = {
//         ...defaultForm,
//         task_requiring_ra: 'Test task',
//         task_duration: null,
//         task_alternative_consideration: 'Test alternative',
//         task_rejection_reason: 'Test rejection',
//       };

//       const {ref} = renderBasicDetails(formWithNullDuration);

//       let isValid;
//       act(() => {
//         isValid = ref.current?.validate();
//       });
//       expect(isValid).toBe(false);
//       expect(mockOnValidate).toHaveBeenCalledWith(false);
//     });
//   });

//   describe('Error Display', () => {
//     it('shows validation errors only after field is touched', () => {
//       renderBasicDetails();

//       // Initially no errors should be visible
//       expect(screen.getByTestId('error-task_requiring_ra')).toHaveTextContent(
//         '',
//       );

//       // After blur, error should appear
//       const taskInput = screen.getByLabelText('Task Requiring R.A.');
//       fireEvent.blur(taskInput, {target: {name: 'task_requiring_ra'}});

//       expect(screen.getByTestId('error-task_requiring_ra')).toHaveTextContent(
//         'This is a mandatory field. Please fill to process.',
//       );
//     });

//     it('displays correct error message for all fields', () => {
//       renderBasicDetails();

//       const fields = [
//         'task_requiring_ra',
//         'task_duration',
//         'task_alternative_consideration',
//         'task_rejection_reason',
//       ];

//       fields.forEach(fieldName => {
//         const input = screen.getByLabelText(
//           fieldName === 'task_requiring_ra'
//             ? 'Task Requiring R.A.'
//             : fieldName === 'task_duration'
//             ? 'Duration of Task'
//             : fieldName === 'task_alternative_consideration'
//             ? 'Alternative Considered to carry out above task'
//             : 'Reason for Rejecting Alternatives',
//         );

//         fireEvent.blur(input, {target: {name: fieldName}});

//         expect(screen.getByTestId(`error-${fieldName}`)).toHaveTextContent(
//           'This is a mandatory field. Please fill to process.',
//         );
//       });
//     });
//   });

//   describe('Component Props', () => {
//     it('works without onValidate callback', () => {
//       const {ref} = renderBasicDetails(defaultForm, undefined);

//       expect(() => {
//         act(() => {
//           ref.current?.validate();
//         });
//       }).not.toThrow();
//     });

//     it('exposes validate method through ref', () => {
//       const {ref} = renderBasicDetails();

//       expect(ref.current).toHaveProperty('validate');
//       expect(typeof ref.current?.validate).toBe('function');
//     });

//     it('handles form updates correctly', () => {
//       const updatedForm = {
//         ...defaultForm,
//         task_requiring_ra: 'Updated task',
//       };

//       const {rerender} = render(
//         <BasicDetails
//           form={defaultForm}
//           setForm={mockSetForm}
//           onValidate={mockOnValidate}
//         />,
//       );

//       expect(screen.getByLabelText('Task Requiring R.A.')).toHaveValue('');

//       rerender(
//         <BasicDetails
//           form={updatedForm}
//           setForm={mockSetForm}
//           onValidate={mockOnValidate}
//         />,
//       );

//       expect(screen.getByLabelText('Task Requiring R.A.')).toHaveValue(
//         'Updated task',
//       );
//     });
//   });

//   describe('Field-specific Validation', () => {
//     it('validates individual fields correctly', () => {
//       renderBasicDetails();

//       // Test task_requiring_ra validation
//       const taskInput = screen.getByLabelText('Task Requiring R.A.');
//       fireEvent.change(taskInput, {
//         target: {name: 'task_requiring_ra', value: ''},
//       });
//       fireEvent.blur(taskInput, {target: {name: 'task_requiring_ra'}});

//       expect(screen.getByTestId('error-task_requiring_ra')).toHaveTextContent(
//         'This is a mandatory field. Please fill to process.',
//       );

//       // Fill the field and check error is cleared
//       fireEvent.change(taskInput, {
//         target: {name: 'task_requiring_ra', value: 'Valid task'},
//       });
//       expect(screen.getByTestId('error-task_requiring_ra')).toHaveTextContent(
//         '',
//       );
//     });

//     it('handles empty string validation correctly', () => {
//       renderBasicDetails();

//       const taskInput = screen.getByLabelText('Task Requiring R.A.');

//       // Set to empty string and trigger blur to show error
//       fireEvent.change(taskInput, {
//         target: {name: 'task_requiring_ra', value: ''},
//       });
//       fireEvent.blur(taskInput, {target: {name: 'task_requiring_ra'}});

//       expect(screen.getByTestId('error-task_requiring_ra')).toHaveTextContent(
//         'This is a mandatory field. Please fill to process.',
//       );
//     });
//   });

//   describe('Risk Type Functionality', () => {
//     it('renders risk fields when type is risk', async () => {
//       const riskForm = {...defaultRiskForm, assessor: 1, vessel_ownership_id: 1};
//       renderBasicDetails(riskForm, mockOnValidate, 'risk');

//       await waitFor(() => {
//         expect(screen.getByText('Assessor')).toBeInTheDocument();
//         expect(screen.getByText('Vessel/Office')).toBeInTheDocument();
//         expect(screen.getByText('Date of Risk Assessment')).toBeInTheDocument();
//         expect(screen.getByText('Approvals Required (if necessary)')).toBeInTheDocument();
//       });
//     });

//     it('does not render risk fields when type is template', () => {
//       renderBasicDetails(defaultForm, mockOnValidate, 'template');

//       expect(screen.queryByText('Assessor')).not.toBeInTheDocument();
//       expect(screen.queryByText('Vessel/Office')).not.toBeInTheDocument();
//       expect(screen.queryByText('Date of Risk Assessment')).not.toBeInTheDocument();
//       expect(screen.queryByText('Approvals Required (if necessary)')).not.toBeInTheDocument();
//     });

//     it('loads vessel, office, and approval options for risk type', async () => {
//       renderBasicDetails(defaultRiskForm, mockOnValidate, 'risk');

//       await waitFor(() => {
//         expect(mockGetVesselsList).toHaveBeenCalled();
//         expect(mockGetOfficesList).toHaveBeenCalled();
//         expect(mockGetApprovalsRequiredList).toHaveBeenCalled();
//       });
//     });

//     it('does not load options for template type', () => {
//       renderBasicDetails(defaultForm, mockOnValidate, 'template');

//       expect(mockGetVesselsList).not.toHaveBeenCalled();
//       expect(mockGetOfficesList).not.toHaveBeenCalled();
//       expect(mockGetApprovalsRequiredList).not.toHaveBeenCalled();
//     });

//     it('handles assessor change correctly', async () => {
//       const riskForm = {...defaultRiskForm};
//       renderBasicDetails(riskForm, mockOnValidate, 'risk');

//       await waitFor(() => {
//         const assessorSelect = screen.getByTestId('assessor-select');
//         fireEvent.change(assessorSelect, {target: {value: '1'}});
//       });

//       expect(mockSetForm).toHaveBeenCalledWith(expect.objectContaining({
//         assessor: 1,
//       }));
//     });

//     it('handles vessel/office change for vessel assessor', async () => {
//       const riskForm = {...defaultRiskForm, assessor: 2};
//       renderBasicDetails(riskForm, mockOnValidate, 'risk');

//       await waitFor(() => {
//         const vesselOfficeSelect = screen.getByTestId('vessel-office-select');
//         fireEvent.change(vesselOfficeSelect, {target: {value: '1'}});
//       });

//       expect(mockSetForm).toHaveBeenCalled();
//     });

//     it('handles vessel/office change for office assessor', async () => {
//       const riskForm = {...defaultRiskForm, assessor: 1};
//       renderBasicDetails(riskForm, mockOnValidate, 'risk');

//       await waitFor(() => {
//         const vesselOfficeSelect = screen.getByTestId('vessel-office-select');
//         fireEvent.change(vesselOfficeSelect, {target: {value: '1'}});
//       });

//       expect(mockSetForm).toHaveBeenCalled();
//     });

//     it('handles date change correctly', async () => {
//       const riskForm = {...defaultRiskForm};
//       renderBasicDetails(riskForm, mockOnValidate, 'risk');

//       await waitFor(() => {
//         const dateInput = screen.getByTestId('date_risk_assessment-input');
//         fireEvent.change(dateInput, {target: {value: '2024-01-15'}});
//       });

//       expect(mockSetForm).toHaveBeenCalled();
//       expect(mockFormatDateToYYYYMMDD).toHaveBeenCalled();
//     });

//     it('handles approval change correctly', async () => {
//       const riskForm = {...defaultRiskForm};
//       renderBasicDetails(riskForm, mockOnValidate, 'risk');

//       await waitFor(() => {
//         const approvalSelect = screen.getByTestId('approvals-required--if-necessary--select');
//         fireEvent.change(approvalSelect, {target: {value: '1'}});
//       });

//       expect(mockSetForm).toHaveBeenCalled();
//     });
//   });

//   describe('Risk Validation', () => {
//     it('validates risk form with all required fields', () => {
//       const completeRiskForm = {
//         ...defaultRiskForm,
//         task_requiring_ra: 'Test task',
//         task_duration: '5',
//         task_alternative_consideration: 'Test alternative',
//         task_rejection_reason: 'Test rejection',
//         assessor: 1,
//         vessel_ownership_id: 1,
//         date_risk_assessment: '2024-01-15',
//         approval_required: [1],
//       };

//       const {ref} = renderBasicDetails(completeRiskForm, mockOnValidate, 'risk');

//       let isValid;
//       act(() => {
//         isValid = ref.current?.validate();
//       });
//       expect(isValid).toBe(true);
//       expect(mockOnValidate).toHaveBeenCalledWith(true);
//     });

//     it('fails validation when assessor is missing', () => {
//       const incompleteRiskForm = {
//         ...defaultRiskForm,
//         task_requiring_ra: 'Test task',
//         task_duration: '5',
//         task_alternative_consideration: 'Test alternative',
//         task_rejection_reason: 'Test rejection',
//         assessor: 0,
//         vessel_ownership_id: 1,
//         date_risk_assessment: '2024-01-15',
//         approval_required: [1],
//       };

//       const {ref} = renderBasicDetails(incompleteRiskForm, mockOnValidate, 'risk');

//       let isValid;
//       act(() => {
//         isValid = ref.current?.validate();
//       });
//       expect(isValid).toBe(false);
//       expect(mockOnValidate).toHaveBeenCalledWith(false);
//     });

//     it('fails validation when vessel_ownership_id is missing', () => {
//       const incompleteRiskForm = {
//         ...defaultRiskForm,
//         task_requiring_ra: 'Test task',
//         task_duration: '5',
//         task_alternative_consideration: 'Test alternative',
//         task_rejection_reason: 'Test rejection',
//         assessor: 1,
//         vessel_ownership_id: 0,
//         date_risk_assessment: '2024-01-15',
//         approval_required: [1],
//       };

//       const {ref} = renderBasicDetails(incompleteRiskForm, mockOnValidate, 'risk');

//       let isValid;
//       act(() => {
//         isValid = ref.current?.validate();
//       });
//       expect(isValid).toBe(false);
//       expect(mockOnValidate).toHaveBeenCalledWith(false);
//     });

//     it('fails validation when date_risk_assessment is missing', () => {
//       const incompleteRiskForm = {
//         ...defaultRiskForm,
//         task_requiring_ra: 'Test task',
//         task_duration: '5',
//         task_alternative_consideration: 'Test alternative',
//         task_rejection_reason: 'Test rejection',
//         assessor: 1,
//         vessel_ownership_id: 1,
//         date_risk_assessment: '',
//         approval_required: [1],
//       };

//       const {ref} = renderBasicDetails(incompleteRiskForm, mockOnValidate, 'risk');

//       let isValid;
//       act(() => {
//         isValid = ref.current?.validate();
//       });
//       expect(isValid).toBe(false);
//       expect(mockOnValidate).toHaveBeenCalledWith(false);
//     });

//     it('fails validation when approval_required is empty', () => {
//       const incompleteRiskForm = {
//         ...defaultRiskForm,
//         task_requiring_ra: 'Test task',
//         task_duration: '5',
//         task_alternative_consideration: 'Test alternative',
//         task_rejection_reason: 'Test rejection',
//         assessor: 1,
//         vessel_ownership_id: 1,
//         date_risk_assessment: '2024-01-15',
//         approval_required: [],
//       };

//       const {ref} = renderBasicDetails(incompleteRiskForm, mockOnValidate, 'risk');

//       let isValid;
//       act(() => {
//         isValid = ref.current?.validate();
//       });
//       expect(isValid).toBe(false);
//       expect(mockOnValidate).toHaveBeenCalledWith(false);
//     });
//   });

//   describe('Utility Functions and Edge Cases', () => {
//     it('renders NoteBox component', () => {
//       renderBasicDetails();

//       expect(screen.getByText(/Note:/)).toBeInTheDocument();
//       expect(screen.getByText(/If the defined job duration exceeds/)).toBeInTheDocument();
//     });

//     it('handles task_duration as number correctly', () => {
//       const formWithNumberDuration = {
//         ...defaultForm,
//         task_duration: 5 as any,
//       };

//       renderBasicDetails(formWithNumberDuration);

//       const durationInput = screen.getByLabelText('Duration of Task');
//       expect(durationInput).toHaveValue('5');
//     });

//     it('handles empty date change', async () => {
//       const riskForm = {...defaultRiskForm, date_risk_assessment: '2024-01-15'};
//       renderBasicDetails(riskForm, mockOnValidate, 'risk');

//       await waitFor(() => {
//         const dateInput = screen.getByTestId('date_risk_assessment-input');
//         fireEvent.change(dateInput, {target: {value: ''}});
//       });

//       expect(mockSetForm).toHaveBeenCalled();
//     });

//     it('handles array selected values in assessor change', async () => {
//       const riskForm = {...defaultRiskForm};
//       renderBasicDetails(riskForm, mockOnValidate, 'risk');

//       await waitFor(() => {
//         const assessorSelect = screen.getByTestId('assessor-select');
//         // Simulate simple selection
//         fireEvent.change(assessorSelect, {target: {value: '2'}});
//       });

//       expect(mockSetForm).toHaveBeenCalled();
//     });

//     it('handles vessel selection with vesselId', async () => {
//       const riskForm = {...defaultRiskForm, assessor: 2};
//       renderBasicDetails(riskForm, mockOnValidate, 'risk');

//       await waitFor(() => {
//         const vesselOfficeSelect = screen.getByTestId('vessel-office-select');
//         fireEvent.change(vesselOfficeSelect, {target: {value: '1'}});
//       });

//       expect(mockSetForm).toHaveBeenCalled();
//     });

//     it('handles single approval selection', async () => {
//       const riskForm = {...defaultRiskForm};
//       renderBasicDetails(riskForm, mockOnValidate, 'risk');

//       await waitFor(() => {
//         const approvalSelect = screen.getByTestId('approvals-required--if-necessary--select');
//         fireEvent.change(approvalSelect, {target: {value: '1'}});
//       });

//       expect(mockSetForm).toHaveBeenCalled();
//     });

//     it('handles error in loading options', async () => {
//       const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
//       mockGetVesselsList.mockRejectedValue(new Error('API Error'));

//       renderBasicDetails(defaultRiskForm, mockOnValidate, 'risk');

//       await waitFor(() => {
//         expect(consoleErrorSpy).toHaveBeenCalledWith('Error loading vessel/office options:', expect.any(Error));
//       });

//       consoleErrorSpy.mockRestore();
//     });

//     it('validates with custom form parameter', () => {
//       const {ref} = renderBasicDetails();

//       const customForm = {
//         ...defaultForm,
//         task_requiring_ra: 'Custom task',
//         task_duration: '3',
//         task_alternative_consideration: 'Custom alternative',
//         task_rejection_reason: 'Custom rejection',
//       };

//       let isValid;
//       act(() => {
//         isValid = ref.current?.validate();
//       });

//       // Should validate the current form (empty), not the custom one
//       expect(isValid).toBe(false);
//     });

//     it('handles blur event without validation callback', () => {
//       const {ref} = renderBasicDetails(defaultForm, undefined);

//       const taskInput = screen.getByLabelText('Task Requiring R.A.');

//       expect(() => {
//         fireEvent.blur(taskInput, {target: {name: 'task_requiring_ra'}});
//       }).not.toThrow();
//     });

//     it('shows max length indicators for textarea fields', () => {
//       renderBasicDetails();

//       // The showMaxLength prop should be passed to InputComponent
//       const alternativeInput = screen.getByTestId('input-task_alternative_consideration');
//       const rejectionInput = screen.getByTestId('input-task_rejection_reason');

//       expect(alternativeInput).toBeInTheDocument();
//       expect(rejectionInput).toBeInTheDocument();
//     });

//     it('handles form updates with validation', () => {
//       renderBasicDetails();

//       const taskInput = screen.getByLabelText('Task Requiring R.A.');
//       fireEvent.change(taskInput, {
//         target: {name: 'task_requiring_ra', value: 'Updated task'},
//       });

//       // Should call validation after form update
//       expect(mockOnValidate).toHaveBeenCalled();
//     });

//     it('handles dropdown change with validation', async () => {
//       const riskForm = {...defaultRiskForm};
//       renderBasicDetails(riskForm, mockOnValidate, 'risk');

//       await waitFor(() => {
//         const assessorSelect = screen.getByTestId('assessor-select');
//         fireEvent.change(assessorSelect, {target: {value: '1'}});
//       });

//       expect(mockOnValidate).toHaveBeenCalled();
//     });

//     it('handles date change with validation', async () => {
//       const riskForm = {...defaultRiskForm};
//       renderBasicDetails(riskForm, mockOnValidate, 'risk');

//       await waitFor(() => {
//         const dateInput = screen.getByTestId('date_risk_assessment-input');
//         fireEvent.change(dateInput, {target: {value: '2024-01-15'}});
//       });

//       expect(mockOnValidate).toHaveBeenCalled();
//     });
//   });

//   describe('Component Structure and Styling', () => {
//     it('renders with correct CSS classes and structure', () => {
//       renderBasicDetails();

//       const container = screen.getByText('Enter Basic RA Details').closest('.ra-negate-padding');
//       expect(container).toBeInTheDocument();
//       expect(container).toHaveClass('ra-negate-padding');
//     });

//     it('renders form with correct structure', () => {
//       renderBasicDetails();

//       const form = document.querySelector('form');
//       expect(form).toBeInTheDocument();
//       expect(form).toHaveClass('ra-negate-padding');
//     });

//     it('renders horizontal rule separator', () => {
//       renderBasicDetails();

//       const hr = document.querySelector('hr');
//       expect(hr).toBeInTheDocument();
//     });

//     it('applies correct styling to title', () => {
//       renderBasicDetails();

//       const title = screen.getByText('Enter Basic RA Details');
//       expect(title).toHaveStyle({
//         color: '#1F4A70',
//         fontSize: '20px',
//         fontWeight: '600',
//       });
//     });
//   });

//   describe('Advanced Risk Field Interactions', () => {
//     it('handles assessor change with array selection', async () => {
//       const riskForm = {...defaultRiskForm};
//       renderBasicDetails(riskForm, mockOnValidate, 'risk');

//       await waitFor(() => {
//         const assessorSelect = screen.getByTestId('assessor-select');
//         fireEvent.change(assessorSelect, {target: {value: '2'}});
//       });

//       expect(mockSetForm).toHaveBeenCalled();
//     });

//     it('handles vessel/office change with vesselId for vessel assessor', async () => {
//       const riskForm = {...defaultRiskForm, assessor: 2};
//       renderBasicDetails(riskForm, mockOnValidate, 'risk');

//       await waitFor(() => {
//         const vesselOfficeSelect = screen.getByTestId('vessel-office-select');
//         fireEvent.change(vesselOfficeSelect, {target: {value: '1'}});
//       });

//       expect(mockSetForm).toHaveBeenCalled();
//     });

//     it('handles approval change with multiple selections', async () => {
//       const riskForm = {...defaultRiskForm};
//       renderBasicDetails(riskForm, mockOnValidate, 'risk');

//       await waitFor(() => {
//         const approvalSelect = screen.getByTestId('approvals-required--if-necessary--select');
//         fireEvent.change(approvalSelect, {target: {value: '1'}});
//       });

//       expect(mockSetForm).toHaveBeenCalled();
//     });

//     it('handles approval change with single selection', async () => {
//       const riskForm = {...defaultRiskForm};
//       renderBasicDetails(riskForm, mockOnValidate, 'risk');

//       await waitFor(() => {
//         const approvalSelect = screen.getByTestId('approvals-required--if-necessary--select');
//         fireEvent.change(approvalSelect, {target: {value: '1'}});
//       });

//       expect(mockSetForm).toHaveBeenCalled();
//     });

//     it('handles empty approval selection', async () => {
//       const riskForm = {...defaultRiskForm};
//       renderBasicDetails(riskForm, mockOnValidate, 'risk');

//       await waitFor(() => {
//         const approvalSelect = screen.getByTestId('approvals-required--if-necessary--select');
//         fireEvent.change(approvalSelect, {target: {value: ''}});
//       });

//       expect(mockSetForm).toHaveBeenCalled();
//     });

//     it('shows vessel options when assessor is vessel', async () => {
//       const riskForm = {...defaultRiskForm, assessor: 2};
//       renderBasicDetails(riskForm, mockOnValidate, 'risk');

//       await waitFor(() => {
//         const vesselOfficeSelect = screen.getByTestId('vessel-office-select');
//         expect(vesselOfficeSelect).toBeInTheDocument();
//       });
//     });

//     it('shows office options when assessor is office', async () => {
//       const riskForm = {...defaultRiskForm, assessor: 1};
//       renderBasicDetails(riskForm, mockOnValidate, 'risk');

//       await waitFor(() => {
//         const vesselOfficeSelect = screen.getByTestId('vessel-office-select');
//         expect(vesselOfficeSelect).toBeInTheDocument();
//       });
//     });

//     it('handles dropdown input change events', async () => {
//       const riskForm = {...defaultRiskForm};
//       renderBasicDetails(riskForm, mockOnValidate, 'risk');

//       await waitFor(() => {
//         const assessorSelect = screen.getByTestId('assessor-select');
//         fireEvent.change(assessorSelect, {target: {value: '1'}});
//       });

//       // Should trigger onChange which calls setForm
//       expect(mockSetForm).toHaveBeenCalled();
//     });

//     it('handles dropdown blur events', async () => {
//       const riskForm = {...defaultRiskForm};
//       renderBasicDetails(riskForm, mockOnValidate, 'risk');

//       await waitFor(() => {
//         const assessorSelect = screen.getByTestId('assessor-select');
//         fireEvent.change(assessorSelect, {target: {value: '1'}});
//       });

//       // Should trigger onChange which calls setForm
//       expect(mockSetForm).toHaveBeenCalled();
//     });

//     it('displays validation errors for risk fields when touched', async () => {
//       const riskForm = {...defaultRiskForm};
//       const {ref} = renderBasicDetails(riskForm, mockOnValidate, 'risk');

//       // Trigger validation to show errors
//       act(() => {
//         ref.current?.validate();
//       });

//       // Should show validation error for empty assessor in the dropdown component
//       await waitFor(() => {
//         const assessorDropdown = screen.getByTestId('dropdown-assessor');
//         expect(assessorDropdown).toBeInTheDocument();
//       });
//     });
//   });

//   describe('Edge Cases and Error Handling', () => {
//     it('handles null/undefined values in form fields', () => {
//       const formWithNulls = {
//         ...defaultRiskForm,
//         task_requiring_ra: null as any,
//         task_duration: undefined as any,
//         assessor: null as any,
//         vessel_ownership_id: null as any,
//       };

//       const {ref} = renderBasicDetails(formWithNulls, mockOnValidate, 'risk');

//       let isValid;
//       act(() => {
//         isValid = ref.current?.validate();
//       });
//       expect(isValid).toBe(false);
//     });

//     it('handles form validation without onValidate callback', () => {
//       const {ref} = renderBasicDetails(defaultRiskForm, undefined, 'risk');

//       expect(() => {
//         act(() => {
//           ref.current?.validate();
//         });
//       }).not.toThrow();
//     });

//     it('handles date change with null date', async () => {
//       const riskForm = {...defaultRiskForm, date_risk_assessment: '2024-01-15'};
//       renderBasicDetails(riskForm, mockOnValidate, 'risk');

//       await waitFor(() => {
//         const dateInput = screen.getByTestId('date_risk_assessment-input');
//         fireEvent.change(dateInput, {target: {value: ''}});
//       });

//       expect(mockSetForm).toHaveBeenCalled();
//     });

//     it('handles vessel/office change with no value', async () => {
//       const riskForm = {...defaultRiskForm, assessor: 1};
//       renderBasicDetails(riskForm, mockOnValidate, 'risk');

//       await waitFor(() => {
//         const vesselOfficeSelect = screen.getByTestId('vessel-office-select');
//         fireEvent.change(vesselOfficeSelect, {target: {value: ''}});
//       });

//       expect(mockSetForm).toHaveBeenCalled();
//     });

//     it('validates field with whitespace-only content', () => {
//       const formWithWhitespace = {
//         ...defaultForm,
//         task_requiring_ra: '   ',
//         task_duration: '\t\n',
//         task_alternative_consideration: '  \n  ',
//         task_rejection_reason: '\t',
//       };

//       const {ref} = renderBasicDetails(formWithWhitespace);

//       let isValid;
//       act(() => {
//         isValid = ref.current?.validate();
//       });
//       expect(isValid).toBe(false);
//     });

//     it('handles task_duration as number type', () => {
//       const formWithNumberDuration = {
//         ...defaultForm,
//         task_duration: 42 as any,
//       };

//       renderBasicDetails(formWithNumberDuration);

//       const durationInput = screen.getByLabelText('Duration of Task');
//       expect(durationInput).toHaveValue('42');
//     });

//     it('handles zero values for numeric fields in risk form', () => {
//       const riskFormWithZeros = {
//         ...defaultRiskForm,
//         assessor: 0,
//         vessel_ownership_id: 0,
//       };

//       const {ref} = renderBasicDetails(riskFormWithZeros, mockOnValidate, 'risk');

//       let isValid;
//       act(() => {
//         isValid = ref.current?.validate();
//       });
//       expect(isValid).toBe(false);
//     });
//   });
// });
