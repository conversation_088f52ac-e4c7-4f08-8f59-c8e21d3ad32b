import React from 'react';
import {render, screen, fireEvent} from '@testing-library/react';
import {DeleteJobModal} from '../../src/components/DeleteJobModal';
import {TemplateForm} from '../../src/types/template';

describe('DeleteJobModal', () => {
  const mockSetForm = jest.fn();
  const mockOnClose = jest.fn();

  const mockForm: TemplateForm = {
    template_job: [
      {
        job_id: 'job-1',
        job_step: 'First Job Step',
        job_hazard: 'First Hazard',
      },
      {
        job_id: 'job-2',
        job_step: 'Second Job Step',
        job_hazard: 'Second Hazard',
      },
      {
        job_id: 'job-3',
        job_step: 'Third Job Step',
        job_hazard: 'Third Hazard',
      },
    ],
  } as TemplateForm;

  const defaultProps = {
    onClose: mockOnClose,
    jobId: 'job-2',
    form: mockForm,
    setForm: mockSetForm,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders modal with correct title and content', () => {
    render(<DeleteJobModal {...defaultProps} />);
    
    expect(screen.getByText('Deleting a Job Step')).toBeInTheDocument();
    expect(screen.getByText('Are you sure you want to delete this job step?')).toBeInTheDocument();
    expect(screen.getByText('This action is permamnent and cannot be undone.')).toBeInTheDocument();
  });

  it('renders Delete Job Step and Cancel buttons', () => {
    render(<DeleteJobModal {...defaultProps} />);
    
    expect(screen.getByRole('button', {name: 'Delete Job Step'})).toBeInTheDocument();
    expect(screen.getByRole('button', {name: 'Cancel'})).toBeInTheDocument();
  });

  it('displays correct job information for the selected job', () => {
    render(<DeleteJobModal {...defaultProps} />);
    
    // Should show JOB 2 (index 1 + 1)
    expect(screen.getByText('JOB 2')).toBeInTheDocument();
    expect(screen.getByText('Second Job Step - Second Hazard')).toBeInTheDocument();
  });

  it('displays correct job information for first job', () => {
    render(<DeleteJobModal {...defaultProps} jobId="job-1" />);
    
    expect(screen.getByText('JOB 1')).toBeInTheDocument();
    expect(screen.getByText('First Job Step - First Hazard')).toBeInTheDocument();
  });

  it('displays correct job information for last job', () => {
    render(<DeleteJobModal {...defaultProps} jobId="job-3" />);
    
    expect(screen.getByText('JOB 3')).toBeInTheDocument();
    expect(screen.getByText('Third Job Step - Third Hazard')).toBeInTheDocument();
  });

  it('calls onClose when Cancel button is clicked', () => {
    render(<DeleteJobModal {...defaultProps} />);
    
    const cancelButton = screen.getByRole('button', {name: 'Cancel'});
    fireEvent.click(cancelButton);
    
    expect(mockOnClose).toHaveBeenCalledWith();
    expect(mockSetForm).not.toHaveBeenCalled();
  });

  it('removes job from form and calls onClose when Delete Job Step is clicked', () => {
    render(<DeleteJobModal {...defaultProps} />);
    
    const deleteButton = screen.getByRole('button', {name: 'Delete Job Step'});
    fireEvent.click(deleteButton);
    
    expect(mockSetForm).toHaveBeenCalledWith(expect.any(Function));
    expect(mockOnClose).toHaveBeenCalledWith();
    
    // Test the function passed to setForm
    const setFormCall = mockSetForm.mock.calls[0][0];
    const updatedForm = setFormCall(mockForm);
    
    expect(updatedForm.template_job).toHaveLength(2);
    expect(updatedForm.template_job.find((job: any) => job.job_id === 'job-2')).toBeUndefined();
    expect(updatedForm.template_job.find((job: any) => job.job_id === 'job-1')).toBeDefined();
    expect(updatedForm.template_job.find((job: any) => job.job_id === 'job-3')).toBeDefined();
  });

  it('handles empty template_job array', () => {
    const emptyForm = {...mockForm, template_job: []};
    render(<DeleteJobModal {...defaultProps} form={emptyForm} />);
    
    expect(screen.getByText('JOB 1')).toBeInTheDocument();
    // Should handle gracefully when no job exists
  });

  it('handles non-existent jobId', () => {
    render(<DeleteJobModal {...defaultProps} jobId="non-existent-job" />);
    
    // Should default to index 0
    expect(screen.getByText('JOB 1')).toBeInTheDocument();
    expect(screen.getByText('First Job Step - First Hazard')).toBeInTheDocument();
  });

  it('handles undefined template_job', () => {
    const formWithoutJobs = {...mockForm, template_job: undefined};
    render(<DeleteJobModal {...defaultProps} form={formWithoutJobs} />);
    
    expect(screen.getByText('JOB 1')).toBeInTheDocument();
  });

  it('renders with correct CSS classes and structure', () => {
    render(<DeleteJobModal {...defaultProps} />);
    
    const alert = screen.getByTestId('error-alert');
    expect(alert).toHaveClass('alert', 'd-flex', 'align-items-center', 'fs-14', 'ra-alert-warning');
    expect(alert).toHaveAttribute('role', 'alert');
    
    const deleteJobBorder = document.querySelector('.delete-job-border');
    expect(deleteJobBorder).toBeInTheDocument();
    
    const jobNumber = document.querySelector('.secondary-color.fs-14.fw-500');
    expect(jobNumber).toBeInTheDocument();
    
    const jobDetails = document.querySelector('.fs-16.fw-600');
    expect(jobDetails).toBeInTheDocument();
  });

  it('renders modal with correct properties', () => {
    render(<DeleteJobModal {...defaultProps} />);
    
    const modal = screen.getByRole('dialog');
    expect(modal).toBeInTheDocument();
    
    const modalBody = screen.getByText('Are you sure you want to delete this job step?').closest('.modal-body');
    expect(modalBody).toHaveClass('complete-project-modal');
  });

  it('handles job deletion when jobId is empty string', () => {
    render(<DeleteJobModal {...defaultProps} jobId="" />);
    
    const deleteButton = screen.getByRole('button', {name: 'Delete Job Step'});
    fireEvent.click(deleteButton);
    
    // Should still call onClose even if jobId is empty
    expect(mockOnClose).toHaveBeenCalledWith();
  });

  it('preserves other form properties when deleting job', () => {
    const formWithOtherProps = {
      ...mockForm,
      template_name: 'Test Template',
      template_description: 'Test Description',
    };
    
    render(<DeleteJobModal {...defaultProps} form={formWithOtherProps} />);
    
    const deleteButton = screen.getByRole('button', {name: 'Delete Job Step'});
    fireEvent.click(deleteButton);
    
    const setFormCall = mockSetForm.mock.calls[0][0];
    const updatedForm = setFormCall(formWithOtherProps);
    
    expect(updatedForm.template_name).toBe('Test Template');
    expect(updatedForm.template_description).toBe('Test Description');
    expect(updatedForm.template_job).toHaveLength(2);
  });
});
