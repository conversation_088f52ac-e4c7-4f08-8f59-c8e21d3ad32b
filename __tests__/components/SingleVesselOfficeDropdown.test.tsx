import React from 'react';
import {render, screen, fireEvent, waitFor} from '@testing-library/react';
import SingleVesselOfficeDropdown from '../../src/components/SingleVesselOfficeDropdown';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';

const mockOptions = [
  {
    label: 'Vessels',
    data: [
      {id: 1, name: 'Vessel 1', vesselId: 101},
      {id: 2, name: 'Vessel 2', vesselId: 102},
    ],
  },
  {
    label: 'Offices',
    data: [
      {id: 3, name: 'Office 1'},
      {id: 4, name: 'Office 2'},
    ],
  },
];

describe('SingleVesselOfficeDropdown', () => {
  const mockOnChange = jest.fn();
  const mockOnBlur = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders with default placeholder', () => {
    render(
      <SingleVesselOfficeDropdown
        value={null}
        options={mockOptions}
        onChange={mockOnChange}
      />,
    );
    expect(
      screen.getByPlaceholderText('Search & Select Vessel/Office'),
    ).toBeInTheDocument();
  });

  it('renders with custom placeholder', () => {
    render(
      <SingleVesselOfficeDropdown
        value={null}
        options={mockOptions}
        onChange={mockOnChange}
        placeholder="Select an option"
      />,
    );
    expect(screen.getByPlaceholderText('Select an option')).toBeInTheDocument();
  });

  it('displays selected value', () => {
    render(
      <SingleVesselOfficeDropdown
        value={{value: 1, label: 'Vessel 1', vesselId: 101}}
        options={mockOptions}
        onChange={mockOnChange}
      />,
    );
    expect(screen.getByDisplayValue('Vessel 1')).toBeInTheDocument();
  });

  it('opens dropdown on input focus', () => {
    render(
      <SingleVesselOfficeDropdown
        value={null}
        options={mockOptions}
        onChange={mockOnChange}
      />,
    );
    const input = screen.getByPlaceholderText('Search & Select Vessel/Office');
    fireEvent.focus(input);
    expect(screen.getByText('Vessels')).toBeInTheDocument();
    expect(screen.getByText('Offices')).toBeInTheDocument();
  });

  it('filters options based on search input', async () => {
    render(
      <SingleVesselOfficeDropdown
        value={null}
        options={mockOptions}
        onChange={mockOnChange}
      />,
    );
    const input = screen.getByPlaceholderText('Search & Select Vessel/Office');
    fireEvent.focus(input);
    fireEvent.change(input, {target: {value: 'Vessel 1'}});

    expect(screen.getByText('Vessel 1')).toBeInTheDocument();
    expect(screen.queryByText('Office 1')).not.toBeInTheDocument();
  });

  it('calls onChange when an option is selected', () => {
    render(
      <SingleVesselOfficeDropdown
        value={null}
        options={mockOptions}
        onChange={mockOnChange}
      />,
    );
    const input = screen.getByPlaceholderText('Search & Select Vessel/Office');
    fireEvent.focus(input);
    fireEvent.click(screen.getByText('Vessel 1'));

    expect(mockOnChange).toHaveBeenCalledWith({
      value: 1,
      label: 'Vessel 1',
      vesselId: 101,
    });
  });

  it('shows error message when isInvalid is true', () => {
    render(
      <SingleVesselOfficeDropdown
        value={null}
        options={mockOptions}
        onChange={mockOnChange}
        isInvalid={true}
        errorMessage="This field is required"
      />,
    );
    expect(screen.getByText('This field is required')).toBeInTheDocument();
  });

  it('clears selection when clear button is clicked', () => {
    render(
      <SingleVesselOfficeDropdown
        value={{value: 1, label: 'Vessel 1', vesselId: 101}}
        options={mockOptions}
        onChange={mockOnChange}
      />,
    );
    const clearButton = screen.getByRole('button');
    fireEvent.click(clearButton);

    expect(mockOnChange).toHaveBeenCalledWith(null);
  });

  it('handles keyboard interactions', () => {
    render(
      <SingleVesselOfficeDropdown
        value={null}
        options={mockOptions}
        onChange={mockOnChange}
      />,
    );
    const input = screen.getByPlaceholderText('Search & Select Vessel/Office');

    // Open dropdown with Enter key
    fireEvent.keyDown(input, {key: 'Enter'});
    expect(screen.getByText('Vessels')).toBeInTheDocument();

    // Close dropdown with Escape key
    fireEvent.keyDown(input, {key: 'Escape'});
    expect(screen.queryByText('Vessels')).not.toBeInTheDocument();
  });

  it('calls onBlur when input loses focus', async () => {
    render(
      <SingleVesselOfficeDropdown
        value={null}
        options={mockOptions}
        onChange={mockOnChange}
        onBlur={mockOnBlur}
      />,
    );
    const input = screen.getByPlaceholderText('Search & Select Vessel/Office');
    fireEvent.focus(input);
    fireEvent.blur(input);

    await waitFor(() => {
      expect(mockOnBlur).toHaveBeenCalled();
    });
  });

  it('closes dropdown on outside click', () => {
    render(
      <SingleVesselOfficeDropdown
        value={null}
        options={mockOptions}
        onChange={mockOnChange}
      />,
    );
    const input = screen.getByPlaceholderText('Search & Select Vessel/Office');
    fireEvent.focus(input);
    expect(screen.getByText('Vessels')).toBeInTheDocument();

    // Click outside
    fireEvent.mouseDown(document.body);
    expect(screen.queryByText('Vessels')).not.toBeInTheDocument();
  });
});
