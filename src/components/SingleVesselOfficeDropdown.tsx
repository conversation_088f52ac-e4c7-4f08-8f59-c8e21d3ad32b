import React, {useState, useMemo, useRef, useEffect} from 'react';
import {ChevronDown, X} from 'react-bootstrap-icons';
import '../styles/components/vessel-and-office-dropdown.scss';
import classNames from 'classnames';

interface GroupedOption {
  label: string;
  data: Array<{
    id: number | string;
    name: string;
    vesselId?: number;
  }>;
}

interface SingleVesselOfficeOption {
  value: number | string;
  label: string;
  vesselId?: number;
}

interface SingleVesselOfficeDropdownItemProps {
  options: GroupedOption[];
  value: SingleVesselOfficeOption | null;
  onChange: (selected: SingleVesselOfficeOption | null) => void;
  onClose: () => void;
  search: string;
  onSearchChange: (search: string) => void;
}

const SingleVesselOfficeDropdownItem: React.FC<SingleVesselOfficeDropdownItemProps> =
  ({options = [], value, onChange, onClose, search, onSearchChange}) => {
    const filteredGroups = useMemo(() => {
      if (!search) {
        return options;
      }
      return options
        .map(group => ({
          ...group,
          data: group.data.filter(option =>
            option.name.toLowerCase().includes(search.toLowerCase()),
          ),
        }))
        .filter(group => group.data.length > 0);
    }, [options, search]);

    const handleOptionSelection = (
      groupLabel: string,
      option: {id: number | string; name: string; vesselId?: number},
    ): void => {
      const selectedOption: SingleVesselOfficeOption = {
        value: option.id,
        label: option.name,
        ...(option.vesselId && {vesselId: option.vesselId}),
      };

      onChange(selectedOption);
      onClose();
    };

    // Close dropdown on outside click
    const dropdownRef = useRef<HTMLDivElement>(null);
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (
          dropdownRef.current &&
          !dropdownRef.current.contains(event.target as Node)
        ) {
          onClose();
        }
      };
      document.addEventListener('mousedown', handleClickOutside);
      return () =>
        document.removeEventListener('mousedown', handleClickOutside);
    }, [onClose]);

    return (
      <div
        ref={dropdownRef}
        className="option-selector-container option-selector-dropdown w-100"
      >
        <div className="option-list-section">
          {filteredGroups.length > 0 ? (
            filteredGroups.map(group => (
              <div key={group.label} className="option-group">
                <div className="option-group-label padding-lt-16">
                  {group.label}
                </div>
                {group.data.map(option => (
                  <div
                    key={option.id}
                    className="option-list-item"
                    role="button"
                    tabIndex={0}
                    onClick={() => handleOptionSelection(group.label, option)}
                    onKeyDown={e => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        handleOptionSelection(group.label, option);
                      }
                    }}
                  >
                    <div className="option-name">{option.name}</div>
                  </div>
                ))}
              </div>
            ))
          ) : (
            <div className="empty-list-message">No options found.</div>
          )}
        </div>
      </div>
    );
  };

interface SingleVesselOfficeDropdownProps {
  value: SingleVesselOfficeOption | null;
  options: GroupedOption[];
  placeholder?: string;
  onChange: (selected: SingleVesselOfficeOption | null) => void;
  onBlur?: () => void;
  isInvalid?: boolean;
  errorMessage?: string;
  label?: string;
}

const SingleVesselOfficeDropdown: React.FC<SingleVesselOfficeDropdownProps> = ({
  value,
  options,
  placeholder = 'Search & Select Vessel/Office',
  onChange,
  onBlur,
  isInvalid = false,
  errorMessage,
  label,
}) => {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [search, setSearch] = useState('');
  const [touched, setTouched] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const handleInputKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      setDropdownOpen(open => !open);
    }
    if (event.key === 'Escape') {
      setDropdownOpen(false);
    }
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newSearch = event.target.value;
    setSearch(newSearch);
    if (!dropdownOpen) {
      setDropdownOpen(true);
    }
  };

  const handleInputFocus = () => {
    setDropdownOpen(true);
  };

  const handleInputBlur = () => {
    // Delay blur to allow for option selection
    setTimeout(() => {
      if (!touched) {
        setTouched(true);
      }
      if (onBlur) {
        onBlur();
      }
    }, 150);
  };

  const handleDropdownClose = () => {
    setDropdownOpen(false);
    setSearch('');
  };

  const handleClearField = (event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();
    onChange(null);
    setDropdownOpen(false);
    setSearch('');
    setTouched(true);
  };

  const handleInputClick = () => {
    if (!dropdownOpen && value) {
      // If dropdown is closed and there's a selected value, clear it
      onChange(null);
      setTouched(true);
    } else {
      // Otherwise, open the dropdown
      setDropdownOpen(true);
    }
  };

  const displayValue = value ? value.label : '';

  return (
    <div className="form-group">
      {label && <label className="form-label">{label}</label>}
      <div className="option-multiselect-root">
        <input
          ref={inputRef}
          type="text"
          className={classNames('form-control fs-14 close-out-resp-field', {
            'is-invalid': isInvalid,
          })}
          placeholder={placeholder}
          value={dropdownOpen ? search : displayValue}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          onBlur={handleInputBlur}
          onKeyDown={handleInputKeyDown}
          onClick={handleInputClick}
          autoComplete="off"
        />
        {!isInvalid && (
          <div
            className={classNames('clear-vessel-office position-absolute', {
              clickable: value,
            })}
          >
            <button
              type="button"
              className="unset"
              onClick={value ? handleClearField : undefined}
            >
              {value ? <X size={18} /> : <ChevronDown size={14} />}
            </button>
          </div>
        )}

        {dropdownOpen && (
          <SingleVesselOfficeDropdownItem
            options={options}
            value={value}
            onChange={onChange}
            onClose={handleDropdownClose}
            search={search}
            onSearchChange={setSearch}
          />
        )}
      </div>
      {isInvalid && errorMessage && (
        <div className="invalid-feedback d-block">{errorMessage}</div>
      )}
    </div>
  );
};

export default SingleVesselOfficeDropdown;
export type {SingleVesselOfficeOption, GroupedOption};
