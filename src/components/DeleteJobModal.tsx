import React, {useEffect, useState} from 'react';
import {<PERSON><PERSON>, Button} from 'react-bootstrap';
import {TemplateForm} from '../types/template';

type Props = {
  onClose: () => void;
  jobId: string;
  form: TemplateForm;
  setForm: (f: any) => void;
};

export const DeleteJobModal: React.FC<Props> = ({
  onClose,
  jobId,
  form,
  setForm,
}) => {
  const [jobIndex, setJobIndex] = useState(0);

  useEffect(() => {
    if (jobId && form.template_job?.length > 0) {
      const index = form.template_job.findIndex(job => job.job_id === jobId);
      if (index !== -1) {
        setJobIndex(index);
      }
    }
  }, [jobId, form.template_job]);
  const handleConfirm = () => {
    // Remove the job from the form
    if (jobId && form.template_job?.length > 0) {
      const updatedJobs = form.template_job.filter(job => job.job_id !== jobId);
      setForm((prev: TemplateForm) => ({
        ...prev,
        template_job: updatedJobs,
      }));
    }
    onClose();
  };

  return (
    <Modal
      show
      onHide={onClose}
      size="lg"
      backdrop="static"
      dialogClassName="top-modal"
    >
      <Modal.Header>
        <Modal.Title>Deleting a Job Step</Modal.Title>
      </Modal.Header>
      <Modal.Body className="complete-project-modal">
        <div
          className="alert d-flex align-items-center fs-14 ra-alert-warning"
          role="alert"
          data-testid="error-alert"
        >
          <div>
            <strong>Are you sure you want to delete this job step?</strong>
            <span> This action is permamnent and cannot be undone.</span>
          </div>
        </div>
        <div className="delete-job-border">
          <div className="secondary-color fs-14 fw-500">JOB {jobIndex + 1}</div>
          <div className="fs-16 fw-600">
            {form?.template_job?.[jobIndex]?.job_step} -{' '}
            {form?.template_job?.[jobIndex]?.job_hazard}
          </div>
        </div>
      </Modal.Body>

      <Modal.Footer>
        <Button
          variant="primary"
          className="me-2 fs-14"
          onClick={handleConfirm}
        >
          Delete Job Step
        </Button>
        <Button
          variant="secondary"
          className="me-2 fs-14"
          onClick={() => onClose()}
        >
          Cancel
        </Button>
      </Modal.Footer>
    </Modal>
  );
};
