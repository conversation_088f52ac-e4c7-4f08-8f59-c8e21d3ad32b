import React from 'react';
import {RadioCheckedIcon, RadioUncheckedIcon} from '../utils/svgIcons';

type FormCheckRadioProps = {
  checked: boolean;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  name: string;
  value: string;
  label?: string;
  disabled?: boolean;
  className?: string;
  id?: string;
};

export const FormCheckRadio: React.FC<FormCheckRadioProps> = ({
  checked,
  onChange,
  name,
  value,
  label,
  disabled = false,
  className = '',
  id = 'form-check-radio',
}) => {
  return (
    <label
      id={id}
      className={`form-check-radio ${className}`}
      style={{
        display: 'flex',
        alignItems: 'center',
        cursor: disabled ? 'not-allowed' : 'pointer',
      }}
    >
      <input
        type="radio"
        name={name}
        value={value}
        checked={checked}
        onChange={onChange}
        disabled={disabled}
        style={{display: 'none'}}
      />
      {checked ? <RadioCheckedIcon /> : <RadioUncheckedIcon />}
      {label && <span style={{paddingLeft: 8}}>{label}</span>}
    </label>
  );
};

export default FormCheckRadio;
