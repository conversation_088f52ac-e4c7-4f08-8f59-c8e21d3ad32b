import {JwtUser} from '../types/user';
import {RISK_ASSESMENT_ROLES as ROLES} from '../constants/roles';

class UserRoleController {
  getConfig(kc: any) {
    const hasRole = (role: string): boolean =>
      kc.realmAccess.roles.includes(role);

    return {
      user: kc.tokenParsed as JwtUser,
      riskAssessment: {
        canCreateNewTemplate: hasRole(ROLES.HAS_RA_CREATE_TEMPLATE_ACCESS),
        // add all the required permission for new building
        hasPermision: hasRole(ROLES.HAS_RA_VIEW_ACCESS),
      },
    };
  }
}

export default UserRoleController;
