import {StepperPage} from '../pages/CreateRA/CreateRA.page';
import TemplateListing from '../pages/RATemplateListing/TemplateListing';
import RAListing from '../pages/RAListing/RAListing';
import RADraftsListing from '../pages/RADrafts/RADraftsListing';
import {UserRoleControllerConfig} from '../types';
import TemplateSelection from '../pages/TemplateSelection/TemplateSelection';

export interface IRoute {
  component?: React.FC;
  path: string;
  redirect?: string;
  childRoutes?: IRoute[];
  isPermission?: boolean;
}

// when running application
const routesConfig = (roleConfig: UserRoleControllerConfig): IRoute[] => [
  {
    path: 'risk-assessment',
    component: RAListing,
    isPermission: roleConfig.riskAssessment.hasPermision,
  },
  {
    path: 'risk-assessment/template-listing',
    component: TemplateListing,
    isPermission: roleConfig.riskAssessment.hasPermision,
  },
  {
    path: 'risk-assessment/drafts',
    component: RADraftsListing,
    isPermission: roleConfig.riskAssessment.hasPermision,
  },
  {
    path: 'risk-assessment/templates/create', // create template
    component: StepperPage,
    isPermission: roleConfig.riskAssessment.hasPermision,
  },
  {
    path: 'risk-assessment/templates/:id', // edit template Draft Mode
    component: StepperPage,
    isPermission: roleConfig.riskAssessment.hasPermision,
  },
  {
    path: 'risk-assessment/risks/create', // create ra
    component: StepperPage,
    isPermission: roleConfig.riskAssessment.hasPermision,
  },
  {
    path: 'risk-assessment/risks/:id', // edit ra Draft Mode
    component: StepperPage,
    isPermission: roleConfig.riskAssessment.hasPermision,
  },
  {
    path: 'risk-assessment/templates/:id/risks/create', // create ra using template
    component: StepperPage,
    isPermission: roleConfig.riskAssessment.hasPermision,
  },
  {
    path: 'risk-assessment/template-selection',
    component: TemplateSelection,
    isPermission: roleConfig.riskAssessment.canCreateNewTemplate,
  },
];

export default routesConfig;

// {
//   path: 'risk-assessment/risks', // create RA without template
//   component: StepperPage,
//   isPermission: roleConfig.riskAssessment.hasPermision,
// },
// {
//   path: 'risk-assessment/risks/:id', // draft RA without template
//   component: StepperPage,
//   isPermission: roleConfig.riskAssessment.hasPermision,
// },
// {
//   path: 'risk-assessment/template/:id/risks', // create RA with template
//   component: StepperPage,
//   isPermission: roleConfig.riskAssessment.hasPermision,
// },
