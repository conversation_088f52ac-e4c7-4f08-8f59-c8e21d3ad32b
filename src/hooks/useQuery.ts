import {useState, useEffect, useCallback, useRef} from 'react';

export type QueryStatus = 'idle' | 'loading' | 'success' | 'error';

export type QueryOptions<TData, TError> = {
  enabled?: boolean;
  retry?: number | boolean;
  retryDelay?: number;
  staleTime?: number;
  cacheTime?: number;
  onSuccess?: (data: TData) => void;
  onError?: (error: TError) => void;
  onSettled?: (data: TData | undefined, error: TError | null) => void;
};

export type QueryResult<TData, TError> = {
  data: TData | undefined;
  error: TError | null;
  isLoading: boolean;
  isError: boolean;
  isSuccess: boolean;
  isIdle: boolean;
  status: QueryStatus;
  refetch: () => Promise<TData>;
  isFetching: boolean;
};

// Cache implementation for storing query results
const queryCache = new Map<
  string,
  {
    data: any;
    lastUpdated: number;
  }
>();

// Helper to check cache and set state
function handleCache<TData>(
  queryKeyString: string,
  staleTime: number,
  enabled: boolean,
  setData: (data: TData) => void,
  setStatus: (status: QueryStatus) => void,
  fetchData: () => Promise<TData>,
) {
  const cachedEntry = queryCache.get(queryKeyString);
  if (cachedEntry) {
    const isStale = Date.now() - cachedEntry.lastUpdated > staleTime;
    setData(cachedEntry.data);
    setStatus('success');
    if (isStale && enabled) {
      (async () => {
        await fetchData();
      })();
    }
    return true;
  }
  return false;
}

// Helper for retry logic
function shouldRetry(retry: number | boolean, retryCount: number) {
  return typeof retry === 'boolean' ? retry : retryCount < retry;
}

// Handles retry logic for fetchData
function handleRetry<TData>(
  retry: number | boolean,
  retryCount: React.MutableRefObject<number>,
  retryDelay: number,
  isMounted: React.MutableRefObject<boolean>,
  fetchData: () => Promise<TData>,
): Promise<TData | undefined> {
  if (shouldRetry(retry, retryCount.current)) {
    retryCount.current++;
    setTimeout(() => {
      if (isMounted.current) {
        (async () => {
          await fetchData();
        })();
      }
    }, retryDelay);
    return Promise.resolve(undefined as TData);
  }
  return Promise.reject(new Error('Query failed after maximum retries'));
}

// Handles error state and callbacks
function handleError<TError, TData>(
  err: unknown,
  setStatus: (status: QueryStatus) => void,
  setError: (error: TError) => void,
  onError?: (error: TError) => void,
  onSettled?: (data: TData | undefined, error: TError | null) => void,
) {
  setStatus('error');
  setError(err as TError);
  onError?.(err as TError);
  onSettled?.(undefined, err as TError);
}

export function useQuery<TData = unknown, TError = Error>(
  queryKey: string | string[],
  queryFn: () => Promise<TData>,
  options: QueryOptions<TData, TError> = {},
): QueryResult<TData, TError> {
  // Convert queryKey to a string for cache key
  const queryKeyString = Array.isArray(queryKey)
    ? queryKey.join('-')
    : queryKey;

  // Destructure options with defaults
  const {
    enabled = true,
    retry = 3,
    retryDelay = 1000,
    staleTime = 0,
    cacheTime = 5 * 60 * 1000, // 5 minutes
    onSuccess,
    onError,
    onSettled,
  } = options;

  // Set up state
  const [status, setStatus] = useState<QueryStatus>('idle');
  const [data, setData] = useState<TData | undefined>(undefined);
  const [error, setError] = useState<TError | null>(null);
  const [isFetching, setIsFetching] = useState<boolean>(false);

  // Keep track of retry count and mounted state
  const retryCount = useRef(0);
  const isMounted = useRef(true);

  // Check if there's cached data available
  useEffect(() => {
    if (
      handleCache<TData>(
        queryKeyString,
        staleTime,
        enabled,
        setData,
        setStatus,
        fetchData,
      )
    )
      return;

    if (enabled) {
      (async () => {
        await fetchData();
      })();
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [queryKeyString, enabled]);

  // Cleanup function to remove from cache after cacheTime
  useEffect(() => {
    const cleanup = setTimeout(() => {
      queryCache.delete(queryKeyString);
    }, cacheTime);

    return () => clearTimeout(cleanup);
  }, [queryKeyString, cacheTime]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMounted.current = false;
    };
  }, []);

  // Main fetch function
  const fetchData = useCallback(async (): Promise<TData> => {
    setIsFetching(true);
    if (status === 'idle') setStatus('loading');
    try {
      const result = await queryFn();
      queryCache.set(queryKeyString, {data: result, lastUpdated: Date.now()});
      setData(result);
      setStatus('success');
      setError(null);
      retryCount.current = 0;
      onSuccess?.(result);
      onSettled?.(result, null);
      return result;
    } catch (err) {
      handleError<TError, TData>(err, setStatus, setError, onError, onSettled);
      const retryResult = await handleRetry(
        retry,
        retryCount,
        retryDelay,
        isMounted,
        fetchData,
      ).catch(() => undefined as TData);
      if (retryResult === undefined) throw err;
      return retryResult;
    } finally {
      if (isMounted.current) {
        setIsFetching(false);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [queryFn, queryKeyString]);
  // Refetch function exposed in the return value
  const refetch = useCallback(() => {
    return fetchData();
  }, [fetchData]);

  return {
    data,
    error,
    isLoading: status === 'loading',
    isError: status === 'error',
    isSuccess: status === 'success',
    isIdle: status === 'idle',
    status,
    refetch,
    isFetching,
  };
}
