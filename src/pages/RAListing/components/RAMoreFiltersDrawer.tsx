import React, {useCallback, useMemo, useState} from 'react';
import {Button, Dropdown, Form} from 'react-bootstrap';
import Drawer from '../../../components/Drawer';
import {PlusIcon, TrashIcon} from '../../../components/icons';
import {
  getRaBasicFiltersFormConfig,
  RAFiltersProps,
  RAFilterValues,
  DateRangeValue,
  FilterOption,
  raFiltersInitialState,
} from './RAFilters';
import CustomDatePickerWithRange from '../../../components/CustomDatePickerWithRange';

import '../../../styles/components/ra-more-filters-drawer.scss';

interface RAMoreFiltersDrawerProps {
  onFilterChange: RAFiltersProps['onFilterChange'];
  optionsData: {
    vessels: FilterOption[];
    vesselCategories: FilterOption[];
    offices: FilterOption[];
  };
}

export const RAMoreFiltersDrawer: React.FC<RAMoreFiltersDrawerProps> = ({
  onFilterChange,
  optionsData: {vessels, vesselCategories, offices},
}) => {
  const [localFilters, setLocalFilters] = useState<RAFilterValues>(
    raFiltersInitialState,
  );

  const [visibleOptionalFilterKeys, setVisibleOptionalFilterKeys] =
    React.useState<string[]>([]);

  const handleFilterChange: RAFiltersProps['onFilterChange'] = useCallback(
    (key, value) => {
      setLocalFilters(prev => ({...prev, [key]: value}));
    },
    [],
  );

  const primaryFilterConfig = useMemo(
    () =>
      getRaBasicFiltersFormConfig(
        {
          filters: localFilters,
          onFilterChange: handleFilterChange,
        },
        {vessels, vesselCategories, offices},
      ),
    [localFilters, handleFilterChange, vessels, vesselCategories, offices],
  );

  const optionalFilterConfig = useMemo(
    () =>
      getRaMoreFiltersFormConfig({
        filters: localFilters,
        onFilterChange: handleFilterChange,
      }),
    [localFilters, handleFilterChange],
  );

  const visibleOptionalFilters = optionalFilterConfig.filter(({key}) =>
    visibleOptionalFilterKeys.includes(key),
  );

  const removeOptionalFilter = (
    key: keyof Pick<RAFilterValues, 'approval_date' | 'assessment_date'>,
  ) => {
    setVisibleOptionalFilterKeys(prev => prev.filter(item => item !== key));
    handleFilterChange(key, null);
  };

  const handleClearFilters = () => {
    setLocalFilters(raFiltersInitialState);
    onFilterChange('approval_status', null);
    onFilterChange('vessel_or_office', null);
    onFilterChange('vessel_category', []);
    onFilterChange('ra_level', null);
    onFilterChange('submitted_on', null);
    onFilterChange('approval_date', null);
    onFilterChange('assessment_date', null);
  };

  const handleUpdateFilters = (filters: RAFilterValues) => {
    onFilterChange('approval_date', filters.approval_date || null);
    onFilterChange('assessment_date', filters.assessment_date || null);
    onFilterChange('approval_status', filters.approval_status || null);
    onFilterChange('vessel_or_office', filters.vessel_or_office || null);
    onFilterChange('vessel_category', filters.vessel_category || []);
    onFilterChange('ra_level', filters.ra_level || null);
    onFilterChange('submitted_on', filters.submitted_on || null);
  };

  return (
    <Drawer
      trigger={
        <Button variant="outline-primary" className="more-filters-button">
          <PlusIcon className="icon" />
          {visibleOptionalFilterKeys.length > 0 ? (
            <span className="label">
              Filters{' '}
              <span className="label-badge">
                {visibleOptionalFilterKeys.length}
              </span>
            </span>
          ) : (
            <span className="label">More Filters</span>
          )}
        </Button>
      }
    >
      {(props: {closeDrawer: () => void}) => (
        <>
          <div className="add-more-filter-header">
            <div>All Filters</div>
            <Dropdown className="add-more-filter-dropdown-container">
              <Dropdown.Toggle
                as="div"
                className="add-more-filter-dropdown-toggle"
              >
                <PlusIcon className="dropdown-icon" />{' '}
                <span className="dropdown-label">Add More Filters</span>
              </Dropdown.Toggle>
              <Dropdown.Menu
                popperConfig={{
                  modifiers: [
                    {name: 'preventOverflow', options: {boundary: 'viewport'}},
                  ],
                }}
              >
                {primaryFilterConfig.map(({key, label}) => (
                  <Dropdown.Item key={key} disabled>
                    {label}
                  </Dropdown.Item>
                ))}
                {optionalFilterConfig.map(({key, label}) => (
                  <Dropdown.Item
                    key={key}
                    onClick={() => {
                      setVisibleOptionalFilterKeys(prev => {
                        if (prev.includes(key)) {
                          return prev.filter(item => item !== key);
                        } else {
                          return [...prev, key];
                        }
                      });
                      handleFilterChange(key, null);
                    }}
                  >
                    {label}
                  </Dropdown.Item>
                ))}
              </Dropdown.Menu>
            </Dropdown>
          </div>

          <main className="ra-more-filters-content">
            <div className="filter-container">
              {primaryFilterConfig
                .filter(config => config.key !== 'search') // Exclude search from more filters
                .map(({key, label, component}) => {
                  return (
                    <div key={key}>
                      <p className="filter-label">{label}</p>
                      <Form.Group className="filter-input" controlId={key}>
                        {component}
                      </Form.Group>
                    </div>
                  );
                })}

              {visibleOptionalFilters.length > 0 && (
                <div className="filters-divider" />
              )}

              {visibleOptionalFilters.map(({key, label, component}) => {
                return (
                  <div key={key}>
                    <div className="d-flex justify-content-between align-items-center">
                      <p className="filter-label">{label}</p>
                      <button
                        className="ra-no-style-btn"
                        onClick={() => removeOptionalFilter(key)}
                      >
                        <TrashIcon />
                      </button>
                    </div>
                    <Form.Group className="filter-input" controlId={key}>
                      {component}
                    </Form.Group>
                  </div>
                );
              })}
            </div>
            <div className="filters-footer">
              <Button
                variant="link"
                className="footer-btn-secondary"
                onClick={() => {
                  handleClearFilters();
                  props.closeDrawer();
                }}
              >
                Clear
              </Button>
              <Button
                variant="primary"
                className="footer-btn-primary"
                onClick={() => {
                  handleUpdateFilters(localFilters);
                  props.closeDrawer();
                }}
              >
                Apply
              </Button>
            </div>
          </main>
        </>
      )}
    </Drawer>
  );
};

export default RAMoreFiltersDrawer;

const getRaMoreFiltersFormConfig = (raFilterProps: {
  filters: Pick<RAFilterValues, 'approval_date' | 'assessment_date'>;
  onFilterChange: (
    key: keyof Pick<RAFilterValues, 'approval_date' | 'assessment_date'>,
    value: DateRangeValue,
  ) => void;
}) => {
  const {filters, onFilterChange} = raFilterProps;
  return [
    {
      key: 'assessment_date' as const,
      label: 'Date of Assessment',
      component: (
        <CustomDatePickerWithRange
          controlId="ra_filters_assessment_date"
          placeholder="Date of Assessment"
          startDate={
            filters.assessment_date?.[0]
              ? new Date(filters.assessment_date?.[0])
              : undefined
          }
          endDate={
            filters.assessment_date?.[1]
              ? new Date(filters.assessment_date?.[1])
              : undefined
          }
          onChange={([start, end]) =>
            onFilterChange('assessment_date', [start ?? null, end ?? null])
          }
        />
      ),
    },
    {
      key: 'approval_date' as const,
      label: 'Approval Date Range',
      component: (
        <CustomDatePickerWithRange
          controlId="ra_filters_approval_date"
          placeholder="Approval Date Range"
          startDate={
            filters.approval_date?.[0]
              ? new Date(filters.approval_date?.[0])
              : undefined
          }
          endDate={
            filters.approval_date?.[1]
              ? new Date(filters.approval_date?.[1])
              : undefined
          }
          onChange={([start, end]) =>
            onFilterChange('approval_date', [start ?? null, end ?? null])
          }
        />
      ),
    },
  ];
};
