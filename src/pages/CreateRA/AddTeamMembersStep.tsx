import React, {forwardRef, useEffect, useImperativeHandle} from 'react';
import {RiskForm} from '../../types/risk';
import {format} from 'date-fns';
import {Card} from 'react-bootstrap';
import {CrewIcon, CrossIcon} from '../../utils/svgIcons';
import SearchCrewMember from '../../components/SearchCrewMember';
import {UsernameProfile} from '../../components/UsernameProfile';
import {CrewMember} from '../../types';
import {useDataStoreContext} from '../../context';
import {getCrewList} from '../../services/services';

interface AddTeamMembersStepRefProps {
  form: RiskForm;
  setForm: React.Dispatch<React.SetStateAction<RiskForm>>;
  onValidate: (isValid: boolean) => void;
  isEdit?: boolean;
}

export interface AddTeamMembersStepRef {
  validate: () => boolean;
}

// --- Helper Components ---
const TeamMemberCard: React.FC<{
  member: any;
  onRemove: (id: number) => void;
  isEdit?: boolean;
}> = ({member, onRemove, isEdit = false}) => (
  <div
    className={`d-flex align-items-center justify-content-between border rounded ${
      isEdit ? 'crew-member-profile-edit' : 'crew-member-profile'
    }`}
  >
    <div className="d-flex align-items-center">
      <UsernameProfile
        username={member.seafarer_name}
        subText={`${member.seafarer_rank} • HK ID: ${member.seafarer_hkid}`}
      />
    </div>
    <button
      type="button"
      className="btn"
      onClick={() => onRemove(member.seafarer_id)}
    >
      <CrossIcon />
    </button>
  </div>
);

const TeamMemberList: React.FC<{
  members: any[];
  onRemove: (id: number) => void;
  isEdit?: boolean;
}> = ({members, onRemove, isEdit = false}) => (
  <div>
    <div className="d-flex flex-wrap gap-24p">
      {members.map(member => (
        <TeamMemberCard
          key={member.seafarer_id}
          member={member}
          onRemove={onRemove}
          isEdit={isEdit}
        />
      ))}
    </div>
  </div>
);

const EmptyTeamMemberState: React.FC = () => (
  <div
    className="d-flex flex-column align-items-center gap-16px"
    data-testid="empty-state"
  >
    <CrewIcon />
    <div className="fs-14 text-muted text-center">
      Search and Add the Team Members <br />
      involved in preparing the Risk Assessment
    </div>
  </div>
);

const TaskHeader: React.FC<{form: RiskForm}> = ({form}) => (
  <div className="d-flex justify-content-between align-items-center mb-3">
    <div>
      <div className="secondary-color fs-20 fw-600">
        {form?.task_requiring_ra || ''}
      </div>
      {'date_risk_assessment' in form && form.date_risk_assessment && (
        <div className="text-muted fs-14">
          Date of Risk Assessment:{' '}
          {format(new Date(form.date_risk_assessment), 'dd MMM yyyy')}
        </div>
      )}
    </div>
  </div>
);

// --- Utility Functions ---
const fetchCrewListForVessel = async (
  form: RiskForm,
  setCrewList: React.Dispatch<React.SetStateAction<any[]>>,
  crewMembersListForRisk: CrewMember[],
) => {
  try {
    if ('vessel_id' in form) {
      const vesselId = form.vessel_id;
      if (vesselId) {
        const crewList = crewMembersListForRisk?.length
          ? crewMembersListForRisk
          : await getCrewList(vesselId);
        setCrewList(crewList);
      }
    }
  } catch (error) {
    console.error('Error fetching options:', error);
  }
};

const addTeamMember = (
  selectedIds: string[],
  form: RiskForm,
  crewList: any[],
  setForm: React.Dispatch<React.SetStateAction<RiskForm>>,
) => {
  if (selectedIds.length === 0 || !('risk_team_member' in form)) return;

  const selectedId = selectedIds[0];
  const selectedCrewMember = crewList.find(
    crew => crew.seafarer_id.toString() === selectedId,
  );
  if (!selectedCrewMember) return;

  const exists = form?.risk_team_member?.some(
    member => member.seafarer_id === selectedCrewMember.seafarer_id,
  );
  if (exists) return;

  const newTeamMember = {
    seafarer_id: selectedCrewMember.seafarer_id,
    seafarer_person_id: selectedCrewMember.seafarer_person_id,
    seafarer_hkid: selectedCrewMember.seafarer_hkid,
    seafarer_rank_id: selectedCrewMember.seafarer_rank_id,
    seafarer_name: selectedCrewMember.seafarer_name,
    seafarer_rank: selectedCrewMember.seafarer_rank,
    seafarer_rank_sort_order: selectedCrewMember.seafarer_rank_sort_order,
  };

  setForm(prevForm => ({
    ...prevForm,
    risk_team_member: [...prevForm.risk_team_member, newTeamMember],
  }));
};

const removeTeamMember = (
  id: number,
  setForm: React.Dispatch<React.SetStateAction<RiskForm>>,
) => {
  setForm(prevForm => ({
    ...prevForm,
    risk_team_member:
      prevForm?.risk_team_member?.filter(m => m.seafarer_id !== id) || [],
  }));
};

const validateTeamMembers = (
  form: RiskForm,
  onValidate: (isValid: boolean) => void,
): boolean => {
  const valid = form?.risk_team_member?.length > 0;
  onValidate(valid);
  return valid;
};

// --- Main Component ---
export const AddTeamMembersStep = forwardRef<
  AddTeamMembersStepRef,
  AddTeamMembersStepRefProps
>(({form, setForm, onValidate, isEdit = false}, ref) => {
  const {
    dataStore: {crewMembersListForRisk},
  } = useDataStoreContext();
  const [crewList, setCrewList] = React.useState<CrewMember[]>([]);

  useEffect(() => {
    fetchCrewListForVessel(form, setCrewList, crewMembersListForRisk);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [form.vessel_id]);

  const validate = () => validateTeamMembers(form, onValidate);

  useImperativeHandle(ref, () => ({
    validate,
  }));

  useEffect(() => {
    if ('risk_team_member' in form) {
      validate();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [form]);

  const handleTeamMemberSelection = (selectedIds: string[]) =>
    addTeamMember(selectedIds, form, crewList, setForm);

  const handleRemoveMember = (id: number) => removeTeamMember(id, setForm);

  const teamMembers = form?.risk_team_member || [];

  return (
    <>
      {!isEdit && (
        <>
          <TaskHeader form={form} />
          <hr style={{marginRight: '-1rem', marginLeft: '-1rem'}} />
          <div className="secondary-color fs-20 fw-600 mb-4">
            Add Team Members
          </div>{' '}
        </>
      )}
      <div className="mb-4 search-crew-bar">
        <SearchCrewMember
          value={[]}
          options={crewList.map(user => ({
            id: user.seafarer_id.toString(),
            full_name: String(user.seafarer_name),
            subText: `${user.seafarer_rank} •HK ID: ${user.seafarer_hkid}`,
          }))}
          placeholder="Search Name, Rank or Email ID to add"
          onChange={handleTeamMemberSelection}
        />
      </div>
      <Card className={`h-60vh ${isEdit ? 'border-0 shadow-none' : ''}`}>
        <Card.Body
          className={
            teamMembers.length === 0
              ? 'd-flex justify-content-center align-items-center'
              : 'p-3'
          }
        >
          {teamMembers.length === 0 ? (
            <EmptyTeamMemberState />
          ) : (
            <TeamMemberList
              members={teamMembers}
              onRemove={handleRemoveMember}
              isEdit={isEdit}
            />
          )}
        </Card.Body>
      </Card>
    </>
  );
});

AddTeamMembersStep.displayName = 'AddTeamMembersStep';
