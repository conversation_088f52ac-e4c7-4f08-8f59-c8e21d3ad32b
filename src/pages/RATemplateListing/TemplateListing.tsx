import React, {useCallback, useState, useMemo} from 'react';
import {ColumnDef} from '@tanstack/react-table';
import InfiniteScrollTable from '../../components/InfiniteScrollTable';
import {TemplateListingHeader} from './components/TemplateListingHeader';
import {
  TemplateListingFilters,
  TemplateFilterValues,
  FilterOption,
} from './components/TemplateListingFilters';
import {useInfiniteQuery} from '../../hooks';
import {getTemplateList} from '../../services/services';
import {
  TemplateCategory,
  TemplateHazard,
  TemplateKeyword,
  TemplateListResponse,
} from '../../types/template';
import {
  cleanObject,
  getDateRangeFilters,
  parseDate,
  withDefault,
} from '../../utils/common';
import SingleBadgePopover from '../../components/SingleBadgePopover';
import BadgeList from '../../components/BadgeListPopover';
import TruncateText from '../../components/TruncateBasicText';
import {TemplateStatus} from '../../enums';
import {BasicUserDetails} from '../../types/user';
import {MostlyUsedCardList} from './components/MostlyUsedCard';
import {ActionDropdownMenu} from './components/ActionDropdownMenu';

import '../../styles/components/template-listing.scss';

export default function TemplateListing() {
  const [filters, setFilters] = useState<TemplateFilterValues>({
    search: '',
    created_by: null,
    created_at: null,
    template_category: null,
    ra_categories: null,
    hazard_categories: null,
  });
  const [sorting, setSorting] = useState<Array<{id: string; desc: boolean}>>([
    {id: 'created_at', desc: true},
  ]);

  let sort_order = undefined;
  if (typeof sorting[0]?.desc === 'boolean') {
    sort_order = sorting[0].desc ? 'DESC' : 'ASC';
  }
  const filtersAndSorters = {
    sort_by: sorting[0]?.id,
    sort_order,
    status: TemplateStatus.PUBLISHED,
    search: filters.search || null,
    created_by: filters.created_by || null,
    ra_categories: filters.ra_categories || null,
    hazard_categories: filters.hazard_categories || null,
    template_category: filters.template_category || null,
    ...getDateRangeFilters('created_at', filters.created_at),
  };

  const {data, isFetchingNextPage, isLoading, fetchNextPage, reset} =
    useInfiniteQuery<
      TemplateListResponse['result']['data'][0],
      TemplateListResponse['result']
    >(getTemplateList, {
      limit: 100,
      ...cleanObject(filtersAndSorters),
    });

  const handleFilterChange = useCallback(
    (
      key: keyof TemplateFilterValues,
      value: FilterOption | string | null | (string | null)[] | number[],
    ) => {
      setFilters(prev => ({...prev, [key]: value}));
    },
    [],
  );

  const columns = useMemo(
    () => getColumns(data.userDetails, reset),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [JSON.stringify(data.userDetails), reset],
  );

  return (
    <div className="ra-template-listing">
      <TemplateListingHeader className="mb-4" />

      <TemplateListingFilters
        filters={filters}
        onFilterChange={handleFilterChange}
      />

      <MostlyUsedCardList />

      <div className="all-templates-title">All Templates</div>

      <InfiniteScrollTable
        columns={columns}
        data={data.data}
        isFetchingNextPage={isFetchingNextPage}
        isLoading={isLoading}
        fetchNextPage={fetchNextPage}
        pagination={data.pagination}
        sorting={{
          sorting,
          onSortingChange: value => {
            if (value.length === 0) {
              setSorting([{id: 'createdAt', desc: true}]);
            } else {
              setSorting(value);
            }
          },
        }}
      />
    </div>
  );
}

export function getColumns(userDetails: BasicUserDetails[], reset: () => void) {
  const columns: ColumnDef<TemplateListResponse['result']['data'][0]>[] = [
    {
      accessorKey: 'task_requiring_ra',
      header: 'Task Required',
      cell: info => (
        <TruncateText
          text={withDefault(String(info.getValue()))}
          maxLength={56}
        />
      ),
      meta: {isSticky: true, stickySide: 'left'},
      enableSorting: true,
      minSize: 420,
    },
    {
      accessorKey: 'template_category',
      header: 'No. of Risk Categories',
      cell: info => {
        const value = info
          .getValue<TemplateCategory[]>()
          .map(category =>
            category.category_is_other
              ? String(category.value)
              : String(category.category?.name),
          )
          .filter(Boolean);
        return value.length > 0 ? (
          <SingleBadgePopover
            items={value}
            label={`${value.length} Categories`}
          />
        ) : (
          withDefault(null)
        );
      },
      enableSorting: false,
      minSize: 200,
    },
    {
      accessorKey: 'template_hazards',
      header: 'No. of Hazard Categories',
      cell: info => {
        const value = info
          .getValue<TemplateHazard[]>()
          .map(category =>
            category.hazard_category_is_other
              ? String(category.value)
              : String(category.hazard_detail?.name),
          )
          .filter(Boolean);
        return value.length > 0 ? (
          <SingleBadgePopover
            items={value}
            label={`${value.length} Categories`}
          />
        ) : (
          withDefault(null)
        );
      },
      enableSorting: false,
      minSize: 200,
    },
    {
      accessorKey: 'created_at',
      header: 'Created on',
      cell: info => withDefault(parseDate(info.getValue() as string)),
      enableSorting: true,
      minSize: 160,
    },
    {
      accessorKey: 'created_by',
      header: 'Created by',
      cell: info => {
        const userId = info.getValue();
        const userEmail = userDetails.find(
          user => user.userId === userId,
        )?.email;
        return (
          <span
            style={{
              textDecorationLine: 'underline',
              color: '#1F4A70',
            }}
          >
            {userEmail ? (
              <TruncateText text={userEmail} maxLength={40} />
            ) : (
              <span className="text-muted">{withDefault(null)}</span>
            )}
          </span>
        );
      },
      enableSorting: false,
      minSize: 200,
    },
    {
      accessorKey: 'template_keywords',
      header: 'Keywords',
      cell: info => {
        const value = info
          .getValue<TemplateKeyword[]>()
          .map(keyword => keyword.name);
        return value.length > 0 ? (
          <BadgeList badges={value} />
        ) : (
          withDefault(null)
        );
      },
      enableSorting: false,
      minSize: 250,
    },
    {
      id: 'action',
      header: 'Action',
      cell: ({row}) => {
        const userId = row.original.created_by;
        const userData = userDetails.find(user => user.userId === userId) ?? {};
        return (
          <ActionDropdownMenu
            data={row.original}
            userDetails={userData}
            onSuccess={reset}
          />
        );
      },
      meta: {
        isSticky: true,
        stickySide: 'right',
        headerAlign: 'center',
      },
      enableSorting: false,
      minSize: 100,
    },
  ];

  return columns;
}
